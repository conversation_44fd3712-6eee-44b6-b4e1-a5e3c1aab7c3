# -*- coding: utf-8 -*-
"""
الأدوات المساعدة لنظام إدارة الصالة الرياضية
"""

import re
import hashlib
import bcrypt
from datetime import datetime, timedelta
from typing import Optional, Union
import sqlite3

def hash_password(password: str) -> str:
    """تشفير كلمة المرور باستخدام bcrypt"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """التحقق من كلمة المرور"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
    except:
        return False

def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف"""
    # إزالة المسافات والرموز
    phone = re.sub(r'[^\d+]', '', phone)
    # التحقق من الطول والتنسيق
    pattern = r'^(\+218|0)?[0-9]{9,10}$'
    return re.match(pattern, phone) is not None

def format_currency(amount: Union[int, float], currency: str = 'د.ل') -> str:
    """تنسيق المبلغ المالي"""
    return f"{amount:,.2f} {currency}"

def format_date(date: datetime, format_type: str = 'short') -> str:
    """تنسيق التاريخ"""
    if format_type == 'short':
        return date.strftime('%Y-%m-%d')
    elif format_type == 'long':
        return date.strftime('%Y-%m-%d %H:%M:%S')
    elif format_type == 'arabic':
        months = {
            1: 'يناير', 2: 'فبراير', 3: 'مارس', 4: 'أبريل',
            5: 'مايو', 6: 'يونيو', 7: 'يوليو', 8: 'أغسطس',
            9: 'سبتمبر', 10: 'أكتوبر', 11: 'نوفمبر', 12: 'ديسمبر'
        }
        return f"{date.day} {months[date.month]} {date.year}"
    return date.strftime('%Y-%m-%d')

def calculate_subscription_end_date(start_date: datetime, duration_months: int) -> datetime:
    """حساب تاريخ انتهاء الاشتراك"""
    # إضافة الشهور مع مراعاة اختلاف عدد الأيام في الشهور
    year = start_date.year
    month = start_date.month + duration_months
    
    # تعديل السنة إذا تجاوز الشهر 12
    while month > 12:
        year += 1
        month -= 12
    
    # التعامل مع الأيام التي قد لا تكون موجودة في الشهر الجديد
    day = start_date.day
    try:
        end_date = start_date.replace(year=year, month=month, day=day)
    except ValueError:
        # إذا كان اليوم غير موجود في الشهر الجديد (مثل 31 فبراير)
        # استخدم آخر يوم في الشهر
        import calendar
        last_day = calendar.monthrange(year, month)[1]
        end_date = start_date.replace(year=year, month=month, day=min(day, last_day))
    
    return end_date

def is_subscription_expired(end_date: datetime) -> bool:
    """التحقق من انتهاء صلاحية الاشتراك"""
    return datetime.now() > end_date

def days_until_expiry(end_date: datetime) -> int:
    """حساب عدد الأيام المتبقية حتى انتهاء الاشتراك"""
    delta = end_date - datetime.now()
    return max(0, delta.days)

def calculate_discount(original_price: float, discount_percentage: float) -> tuple:
    """حساب الخصم والسعر النهائي"""
    discount_amount = original_price * (discount_percentage / 100)
    final_price = original_price - discount_amount
    return discount_amount, final_price

def generate_member_id() -> str:
    """توليد رقم عضوية فريد"""
    import time
    timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
    microseconds = str(int(time.time() * 1000000))[-6:]  # آخر 6 أرقام من الميكروثانية
    return f"GYM{timestamp}{microseconds}"

def sanitize_filename(filename: str) -> str:
    """تنظيف اسم الملف من الأحرف غير المسموحة"""
    # إزالة الأحرف غير المسموحة في أسماء الملفات
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename

def get_age_from_birthdate(birthdate: datetime) -> int:
    """حساب العمر من تاريخ الميلاد"""
    today = datetime.now()
    age = today.year - birthdate.year
    if today.month < birthdate.month or (today.month == birthdate.month and today.day < birthdate.day):
        age -= 1
    return age

class DatabaseError(Exception):
    """استثناء خاص بأخطاء قاعدة البيانات"""
    pass

class ValidationError(Exception):
    """استثناء خاص بأخطاء التحقق من البيانات"""
    pass
