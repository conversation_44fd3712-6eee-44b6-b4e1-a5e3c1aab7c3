# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات لنظام إدارة الصالة الرياضية
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any

import config
from utils.helpers import hash_password, DatabaseError

class DatabaseManager:
    """مدير قاعدة البيانات"""
    
    def __init__(self):
        self.db_path = config.DATABASE_PATH
        self.connection = None
        
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
            return self.connection
        except sqlite3.Error as e:
            raise DatabaseError(f"خطأ في الاتصال بقاعدة البيانات: {e}")
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, query: str, params: tuple = ()) -> sqlite3.Cursor:
        """تنفيذ استعلام SQL"""
        try:
            if not self.connection:
                self.connect()
            cursor = self.connection.cursor()
            cursor.execute(query, params)
            self.connection.commit()
            return cursor
        except sqlite3.Error as e:
            if self.connection:
                self.connection.rollback()
            raise DatabaseError(f"خطأ في تنفيذ الاستعلام: {e}")
    
    def fetch_one(self, query: str, params: tuple = ()) -> Optional[Dict]:
        """جلب سجل واحد"""
        cursor = self.execute_query(query, params)
        row = cursor.fetchone()
        return dict(row) if row else None
    
    def fetch_all(self, query: str, params: tuple = ()) -> List[Dict]:
        """جلب جميع السجلات"""
        cursor = self.execute_query(query, params)
        rows = cursor.fetchall()
        return [dict(row) for row in rows]
    
    def initialize_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        self.connect()
        
        # إنشاء جدول المسؤولين
        self.create_admin_users_table()
        
        # إنشاء جدول الأعضاء
        self.create_members_table()
        
        # إنشاء جدول خطط الاشتراك
        self.create_subscription_plans_table()
        
        # إنشاء جدول الاشتراكات
        self.create_subscriptions_table()
        
        # إنشاء جدول المدفوعات
        self.create_payments_table()
        
        # إنشاء جدول الحضور
        self.create_attendance_table()
        
        # إنشاء المسؤول الافتراضي
        self.create_default_admin()
        
        print("تم إنشاء قاعدة البيانات والجداول بنجاح")
    
    def create_admin_users_table(self):
        """إنشاء جدول المسؤولين"""
        query = """
        CREATE TABLE IF NOT EXISTS admin_users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP
        )
        """
        self.execute_query(query)
    
    def create_members_table(self):
        """إنشاء جدول الأعضاء"""
        query = """
        CREATE TABLE IF NOT EXISTS members (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id TEXT UNIQUE NOT NULL,
            name TEXT NOT NULL,
            phone TEXT,
            email TEXT,
            address TEXT,
            birthdate DATE,
            registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'نشط' CHECK (status IN ('نشط', 'معلق', 'منتهي')),
            notes TEXT
        )
        """
        self.execute_query(query)
    
    def create_subscription_plans_table(self):
        """إنشاء جدول خطط الاشتراك"""
        query = """
        CREATE TABLE IF NOT EXISTS subscription_plans (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            duration_months INTEGER NOT NULL,
            price_lyd REAL NOT NULL,
            discount_percentage REAL DEFAULT 0,
            final_price_lyd REAL NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """
        self.execute_query(query)
    
    def create_subscriptions_table(self):
        """إنشاء جدول الاشتراكات"""
        query = """
        CREATE TABLE IF NOT EXISTS subscriptions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id INTEGER NOT NULL,
            plan_id INTEGER NOT NULL,
            start_date DATE NOT NULL,
            end_date DATE NOT NULL,
            status TEXT DEFAULT 'نشط' CHECK (status IN ('نشط', 'منتهي', 'معلق')),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members (id),
            FOREIGN KEY (plan_id) REFERENCES subscription_plans (id)
        )
        """
        self.execute_query(query)
    
    def create_payments_table(self):
        """إنشاء جدول المدفوعات"""
        query = """
        CREATE TABLE IF NOT EXISTS payments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id INTEGER NOT NULL,
            subscription_id INTEGER,
            amount_lyd REAL NOT NULL,
            payment_method TEXT NOT NULL CHECK (payment_method IN ('نقدي', 'بطاقة', 'تحويل', 'أخرى')),
            payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            status TEXT DEFAULT 'مكتملة' CHECK (status IN ('مكتملة', 'معلقة', 'مستردة')),
            transaction_id TEXT,
            notes TEXT,
            FOREIGN KEY (member_id) REFERENCES members (id),
            FOREIGN KEY (subscription_id) REFERENCES subscriptions (id)
        )
        """
        self.execute_query(query)
    
    def create_attendance_table(self):
        """إنشاء جدول الحضور"""
        query = """
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            member_id INTEGER NOT NULL,
            date DATE NOT NULL,
            time_in TIME NOT NULL,
            time_out TIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (member_id) REFERENCES members (id)
        )
        """
        self.execute_query(query)
    
    def create_default_admin(self):
        """إنشاء المسؤول الافتراضي"""
        # التحقق من وجود مسؤول
        admin = self.fetch_one("SELECT * FROM admin_users WHERE username = ?", ("admin",))
        
        if not admin:
            # إنشاء مسؤول افتراضي
            password_hash = hash_password("admin123")
            query = """
            INSERT INTO admin_users (username, password_hash)
            VALUES (?, ?)
            """
            self.execute_query(query, ("admin", password_hash))
            print("تم إنشاء المسؤول الافتراضي - اسم المستخدم: admin، كلمة المرور: admin123")
    
    def backup_database(self, backup_path: str = None) -> str:
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = config.BACKUP_DIR / f"gym_backup_{timestamp}.db"
        
        try:
            # نسخ ملف قاعدة البيانات
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return str(backup_path)
        except Exception as e:
            raise DatabaseError(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
    
    def restore_database(self, backup_path: str):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            if not os.path.exists(backup_path):
                raise DatabaseError("ملف النسخة الاحتياطية غير موجود")
            
            # إغلاق الاتصال الحالي
            self.disconnect()
            
            # نسخ ملف النسخة الاحتياطية
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            # إعادة الاتصال
            self.connect()
            
        except Exception as e:
            raise DatabaseError(f"خطأ في استعادة النسخة الاحتياطية: {e}")
    
    def __enter__(self):
        """دعم استخدام with statement"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """إغلاق الاتصال عند انتهاء with statement"""
        self.disconnect()
