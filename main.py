# -*- coding: utf-8 -*-
"""
نظام إدارة الصالة الرياضية - الملف الرئيسي
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt5.QtWidgets import QApplication, QSplashScreen, QLabel
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QPixmap, QFont

import config
from database.database_manager import DatabaseManager
from ui.login_window import LoginWindow

class GymManagementApp:
    """الفئة الرئيسية لتطبيق إدارة الصالة الرياضية"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.app.setApplicationName(config.APP_NAME)
        self.app.setApplicationVersion(config.APP_VERSION)
        
        # تعيين الخط الافتراضي للتطبيق
        font = QFont(config.FONTS['main'], config.FONTS['size_normal'])
        self.app.setFont(font)
        
        # تعيين اتجاه النص من اليمين إلى اليسار للعربية
        self.app.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد قاعدة البيانات
        self.setup_database()
        
        # إنشاء شاشة البداية
        self.show_splash_screen()
        
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        try:
            self.db_manager = DatabaseManager()
            self.db_manager.initialize_database()
            print("تم إعداد قاعدة البيانات بنجاح")
        except Exception as e:
            print(f"خطأ في إعداد قاعدة البيانات: {e}")
            sys.exit(1)
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        # إنشاء شاشة البداية
        splash = QSplashScreen()
        splash.setFixedSize(400, 300)
        
        # إنشاء تسمية للنص
        label = QLabel(splash)
        label.setAlignment(Qt.AlignCenter)
        label.setStyleSheet(f"""
            QLabel {{
                background-color: {config.COLORS['primary']};
                color: white;
                font-size: {config.FONTS['size_title']}px;
                font-weight: bold;
                border-radius: 10px;
                padding: 20px;
            }}
        """)
        label.setText(f"{config.APP_NAME}\nالإصدار {config.APP_VERSION}\n\nجاري التحميل...")
        label.setGeometry(0, 0, 400, 300)
        
        splash.show()
        
        # تأخير لعرض شاشة البداية
        QTimer.singleShot(2000, lambda: self.show_login_window(splash))
        
    def show_login_window(self, splash):
        """عرض نافذة تسجيل الدخول"""
        splash.close()
        
        self.login_window = LoginWindow(self.db_manager)
        self.login_window.show()
        
    def run(self):
        """تشغيل التطبيق"""
        return self.app.exec_()

def main():
    """الدالة الرئيسية"""
    try:
        app = GymManagementApp()
        sys.exit(app.run())
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
