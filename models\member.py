# -*- coding: utf-8 -*-
"""
نموذج العضو في نظام إدارة الصالة الرياضية
"""

from datetime import datetime, date
from typing import Optional, List, Dict
from dataclasses import dataclass

from utils.helpers import validate_email, validate_phone, generate_member_id, get_age_from_birthdate

@dataclass
class Member:
    """نموذج بيانات العضو"""
    id: Optional[int] = None
    member_id: Optional[str] = None
    name: str = ""
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    birthdate: Optional[date] = None
    registration_date: Optional[datetime] = None
    status: str = "نشط"
    notes: Optional[str] = None
    
    def __post_init__(self):
        """التحقق من البيانات بعد الإنشاء"""
        if not self.member_id:
            self.member_id = generate_member_id()
        
        if not self.registration_date:
            self.registration_date = datetime.now()
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.name or len(self.name.strip()) < 2:
            errors.append("اسم العضو مطلوب ويجب أن يكون أكثر من حرفين")
        
        if self.email and not validate_email(self.email):
            errors.append("البريد الإلكتروني غير صحيح")
        
        if self.phone and not validate_phone(self.phone):
            errors.append("رقم الهاتف غير صحيح")
        
        if self.birthdate and self.birthdate > date.today():
            errors.append("تاريخ الميلاد لا يمكن أن يكون في المستقبل")
        
        if self.status not in ["نشط", "معلق", "منتهي"]:
            errors.append("حالة العضوية غير صحيحة")
        
        return errors
    
    @property
    def age(self) -> Optional[int]:
        """حساب عمر العضو"""
        if self.birthdate:
            return get_age_from_birthdate(datetime.combine(self.birthdate, datetime.min.time()))
        return None
    
    @property
    def is_active(self) -> bool:
        """التحقق من كون العضو نشطاً"""
        return self.status == "نشط"
    
    def to_dict(self) -> Dict:
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'member_id': self.member_id,
            'name': self.name,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'birthdate': self.birthdate.isoformat() if self.birthdate else None,
            'registration_date': self.registration_date.isoformat() if self.registration_date else None,
            'status': self.status,
            'notes': self.notes
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'Member':
        """إنشاء عضو من قاموس البيانات"""
        member = cls()
        member.id = data.get('id')
        member.member_id = data.get('member_id')
        member.name = data.get('name', '')
        member.phone = data.get('phone')
        member.email = data.get('email')
        member.address = data.get('address')
        
        # تحويل التواريخ
        if data.get('birthdate'):
            if isinstance(data['birthdate'], str):
                member.birthdate = datetime.fromisoformat(data['birthdate']).date()
            else:
                member.birthdate = data['birthdate']
        
        if data.get('registration_date'):
            if isinstance(data['registration_date'], str):
                member.registration_date = datetime.fromisoformat(data['registration_date'])
            else:
                member.registration_date = data['registration_date']
        
        member.status = data.get('status', 'نشط')
        member.notes = data.get('notes')
        
        return member
