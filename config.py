# -*- coding: utf-8 -*-
"""
ملف التكوين الرئيسي لنظام إدارة الصالة الرياضية
"""

import os
from pathlib import Path

# مسارات التطبيق
BASE_DIR = Path(__file__).parent
DATABASE_DIR = BASE_DIR / "database"
REPORTS_DIR = BASE_DIR / "reports"
RESOURCES_DIR = BASE_DIR / "resources"

# إعدادات قاعدة البيانات
DATABASE_NAME = "gym_management.db"
DATABASE_PATH = DATABASE_DIR / DATABASE_NAME

# إعدادات التطبيق
APP_NAME = "نظام إدارة الصالة الرياضية"
APP_VERSION = "1.0.0"
APP_AUTHOR = "مطور النظام"

# إعدادات الواجهة
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
MIN_WINDOW_WIDTH = 1000
MIN_WINDOW_HEIGHT = 600

# الألوان الرئيسية
COLORS = {
    'primary': '#2196F3',      # أزرق رئيسي
    'secondary': '#4CAF50',    # أخضر ثانوي
    'background': '#FFFFFF',   # خلفية بيضاء
    'surface': '#F5F5F5',      # سطح رمادي فاتح
    'text_primary': '#212121', # نص رئيسي
    'text_secondary': '#757575', # نص ثانوي
    'error': '#F44336',        # أحمر للأخطاء
    'warning': '#FF9800',      # برتقالي للتحذيرات
    'success': '#4CAF50',      # أخضر للنجاح
}

# إعدادات الخطوط
FONTS = {
    'main': 'Arial',
    'size_small': 9,
    'size_normal': 11,
    'size_large': 14,
    'size_title': 16,
}

# إعدادات التقارير
REPORT_FORMATS = ['PDF', 'Excel', 'CSV']
DEFAULT_CURRENCY = 'د.ل'  # دينار ليبي

# إعدادات النسخ الاحتياطي
BACKUP_DIR = BASE_DIR / "backups"
MAX_BACKUP_FILES = 10

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات المطلوبة إذا لم تكن موجودة"""
    directories = [DATABASE_DIR, REPORTS_DIR, RESOURCES_DIR, BACKUP_DIR]
    for directory in directories:
        directory.mkdir(exist_ok=True)

# تشغيل إنشاء المجلدات عند استيراد الملف
create_directories()
