# نظام إدارة الصالة الرياضية - إصدار مسؤول النظام

## وصف المشروع
تطبيق مكتبي محلي مخصص لمسؤول النظام في الصالة الرياضية، يوفر إدارة شاملة وفعالة لجميع جوانب الصالة الرياضية.

## المميزات الرئيسية
- إدارة الأعضاء والاشتراكات
- إدارة خطط الاشتراك والأسعار
- نظام المدفوعات والفواتير
- التقارير المالية والإحصائيات
- نظام النسخ الاحتياطي
- واجهة مستخدم عربية حديثة

## التقنيات المستخدمة
- Python 3.8+
- PyQt5 للواجهة الرسومية
- SQLite لقاعدة البيانات
- bcrypt لتشفير كلمات المرور
- pandas & matplotlib للتقارير والرسوم البيانية

## متطلبات التشغيل
- Windows 10 أو أحدث
- Python 3.8+ (للتطوير فقط)

## التثبيت والتشغيل

### الطريقة الأولى (باستخدام الملفات المساعدة):
1. تشغيل `install_requirements.bat` لتثبيت المتطلبات
2. تشغيل `run_app.bat` لتشغيل التطبيق

### الطريقة الثانية (يدوياً):
1. تثبيت المتطلبات: `pip install PyQt5 bcrypt`
2. تشغيل التطبيق: `python main.py`

### بيانات تسجيل الدخول الافتراضية:
- اسم المستخدم: `admin`
- كلمة المرور: `admin123`

### إضافة بيانات تجريبية:
لإضافة بيانات تجريبية للاختبار، قم بتشغيل:
```bash
python database/sample_data.py
```

## بناء التطبيق للتوزيع
```bash
pyinstaller --onefile --windowed --name "GymManager" main.py
```

## هيكل المشروع
```
offlineapp/
├── main.py                 # نقطة البداية الرئيسية
├── database/              # إدارة قاعدة البيانات
├── ui/                    # واجهات المستخدم
├── models/                # نماذج البيانات
├── utils/                 # الأدوات المساعدة
├── resources/             # الموارد (أيقونات، صور)
└── reports/               # التقارير المُصدرة
```

## الترخيص
جميع الحقوق محفوظة
