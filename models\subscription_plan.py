# -*- coding: utf-8 -*-
"""
نموذج خطة الاشتراك في نظام إدارة الصالة الرياضية
"""

from datetime import datetime
from typing import Optional, List, Dict
from dataclasses import dataclass

from utils.helpers import calculate_discount

@dataclass
class SubscriptionPlan:
    """نموذج بيانات خطة الاشتراك"""
    id: Optional[int] = None
    name: str = ""
    duration_months: int = 1
    price_lyd: float = 0.0
    discount_percentage: float = 0.0
    final_price_lyd: float = 0.0
    is_active: bool = True
    description: Optional[str] = None
    created_at: Optional[datetime] = None
    
    def __post_init__(self):
        """حساب السعر النهائي بعد الخصم"""
        if not self.created_at:
            self.created_at = datetime.now()
        
        self.calculate_final_price()
    
    def calculate_final_price(self):
        """حساب السعر النهائي بعد الخصم"""
        if self.discount_percentage > 0:
            _, self.final_price_lyd = calculate_discount(self.price_lyd, self.discount_percentage)
        else:
            self.final_price_lyd = self.price_lyd
    
    def validate(self) -> List[str]:
        """التحقق من صحة البيانات"""
        errors = []
        
        if not self.name or len(self.name.strip()) < 2:
            errors.append("اسم الخطة مطلوب ويجب أن يكون أكثر من حرفين")
        
        if self.duration_months <= 0:
            errors.append("مدة الاشتراك يجب أن تكون أكبر من صفر")
        
        if self.price_lyd < 0:
            errors.append("سعر الاشتراك لا يمكن أن يكون سالباً")
        
        if self.discount_percentage < 0 or self.discount_percentage > 100:
            errors.append("نسبة الخصم يجب أن تكون بين 0 و 100")
        
        return errors
    
    @property
    def discount_amount(self) -> float:
        """حساب مبلغ الخصم"""
        if self.discount_percentage > 0:
            discount_amount, _ = calculate_discount(self.price_lyd, self.discount_percentage)
            return discount_amount
        return 0.0
    
    @property
    def monthly_price(self) -> float:
        """حساب السعر الشهري"""
        return self.final_price_lyd / self.duration_months if self.duration_months > 0 else 0.0
    
    def to_dict(self) -> Dict:
        """تحويل البيانات إلى قاموس"""
        return {
            'id': self.id,
            'name': self.name,
            'duration_months': self.duration_months,
            'price_lyd': self.price_lyd,
            'discount_percentage': self.discount_percentage,
            'final_price_lyd': self.final_price_lyd,
            'is_active': self.is_active,
            'description': self.description,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SubscriptionPlan':
        """إنشاء خطة اشتراك من قاموس البيانات"""
        plan = cls()
        plan.id = data.get('id')
        plan.name = data.get('name', '')
        plan.duration_months = data.get('duration_months', 1)
        plan.price_lyd = data.get('price_lyd', 0.0)
        plan.discount_percentage = data.get('discount_percentage', 0.0)
        plan.final_price_lyd = data.get('final_price_lyd', 0.0)
        plan.is_active = data.get('is_active', True)
        plan.description = data.get('description')
        
        if data.get('created_at'):
            if isinstance(data['created_at'], str):
                plan.created_at = datetime.fromisoformat(data['created_at'])
            else:
                plan.created_at = data['created_at']
        
        return plan
