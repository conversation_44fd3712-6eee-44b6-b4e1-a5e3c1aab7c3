# -*- coding: utf-8 -*-
"""
إضافة بيانات تجريبية لنظام إدارة الصالة الرياضية
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from datetime import datetime, date, timedelta
import random

from database.database_manager import DatabaseManager
from utils.helpers import generate_member_id

def add_sample_subscription_plans(db_manager):
    """إضافة خطط اشتراك تجريبية"""
    plans = [
        {
            'name': 'اشتراك شهري',
            'duration_months': 1,
            'price_lyd': 100.0,
            'discount_percentage': 0.0,
            'final_price_lyd': 100.0,
            'description': 'اشتراك شهري للصالة الرياضية'
        },
        {
            'name': 'اشتراك ربع سنوي',
            'duration_months': 3,
            'price_lyd': 270.0,
            'discount_percentage': 10.0,
            'final_price_lyd': 243.0,
            'description': 'اشتراك لثلاثة أشهر مع خصم 10%'
        },
        {
            'name': 'اشتراك نصف سنوي',
            'duration_months': 6,
            'price_lyd': 500.0,
            'discount_percentage': 15.0,
            'final_price_lyd': 425.0,
            'description': 'اشتراك لستة أشهر مع خصم 15%'
        },
        {
            'name': 'اشتراك سنوي',
            'duration_months': 12,
            'price_lyd': 900.0,
            'discount_percentage': 25.0,
            'final_price_lyd': 675.0,
            'description': 'اشتراك سنوي مع خصم 25%'
        },
        {
            'name': 'اشتراك طلابي',
            'duration_months': 1,
            'price_lyd': 80.0,
            'discount_percentage': 20.0,
            'final_price_lyd': 64.0,
            'description': 'اشتراك شهري للطلاب مع خصم خاص'
        }
    ]
    
    for plan in plans:
        query = """
        INSERT INTO subscription_plans 
        (name, duration_months, price_lyd, discount_percentage, final_price_lyd, description)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        db_manager.execute_query(query, (
            plan['name'], plan['duration_months'], plan['price_lyd'],
            plan['discount_percentage'], plan['final_price_lyd'], plan['description']
        ))
    
    print("تم إضافة خطط الاشتراك التجريبية")

def add_sample_members(db_manager):
    """إضافة أعضاء تجريبيين"""
    members = [
        {
            'name': 'أحمد محمد علي',
            'phone': '0912345678',
            'email': '<EMAIL>',
            'address': 'طرابلس - حي الأندلس',
            'birthdate': date(1990, 5, 15)
        },
        {
            'name': 'فاطمة سالم الزهراني',
            'phone': '0923456789',
            'email': '<EMAIL>',
            'address': 'بنغازي - حي الصابري',
            'birthdate': date(1995, 8, 22)
        },
        {
            'name': 'محمد عبدالله القذافي',
            'phone': '0934567890',
            'email': '<EMAIL>',
            'address': 'مصراتة - وسط المدينة',
            'birthdate': date(1988, 12, 3)
        },
        {
            'name': 'عائشة أحمد البوسيفي',
            'phone': '0945678901',
            'email': '<EMAIL>',
            'address': 'سبها - حي الجديد',
            'birthdate': date(1992, 3, 18)
        },
        {
            'name': 'يوسف علي المبروك',
            'phone': '0956789012',
            'email': '<EMAIL>',
            'address': 'الزاوية - حي النصر',
            'birthdate': date(1985, 7, 9)
        },
        {
            'name': 'مريم سعد الطويل',
            'phone': '0967890123',
            'email': '<EMAIL>',
            'address': 'طرابلس - حي الدهماني',
            'birthdate': date(1998, 11, 27)
        }
    ]
    
    member_ids = []
    for member in members:
        member_id = generate_member_id()
        query = """
        INSERT INTO members 
        (member_id, name, phone, email, address, birthdate)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        cursor = db_manager.execute_query(query, (
            member_id, member['name'], member['phone'], 
            member['email'], member['address'], member['birthdate']
        ))
        member_ids.append(cursor.lastrowid)
    
    print("تم إضافة الأعضاء التجريبيين")
    return member_ids

def add_sample_subscriptions_and_payments(db_manager, member_ids):
    """إضافة اشتراكات ومدفوعات تجريبية"""
    # الحصول على خطط الاشتراك
    plans = db_manager.fetch_all("SELECT * FROM subscription_plans")
    
    payment_methods = ['نقدي', 'بطاقة', 'تحويل']
    
    for member_id in member_ids:
        # اختيار خطة عشوائية
        plan = random.choice(plans)
        
        # تاريخ بداية عشوائي في آخر 3 أشهر
        start_date = datetime.now() - timedelta(days=random.randint(1, 90))
        end_date = start_date + timedelta(days=plan['duration_months'] * 30)
        
        # إضافة الاشتراك
        subscription_query = """
        INSERT INTO subscriptions 
        (member_id, plan_id, start_date, end_date)
        VALUES (?, ?, ?, ?)
        """
        cursor = db_manager.execute_query(subscription_query, (
            member_id, plan['id'], start_date.date(), end_date.date()
        ))
        subscription_id = cursor.lastrowid
        
        # إضافة الدفعة
        payment_method = random.choice(payment_methods)
        payment_query = """
        INSERT INTO payments 
        (member_id, subscription_id, amount_lyd, payment_method, payment_date)
        VALUES (?, ?, ?, ?, ?)
        """
        db_manager.execute_query(payment_query, (
            member_id, subscription_id, plan['final_price_lyd'], 
            payment_method, start_date
        ))
        
        # إضافة بعض سجلات الحضور العشوائية
        for _ in range(random.randint(5, 20)):
            attendance_date = start_date + timedelta(days=random.randint(0, 30))
            time_in = f"{random.randint(6, 10):02d}:{random.randint(0, 59):02d}"
            time_out = f"{random.randint(19, 23):02d}:{random.randint(0, 59):02d}"
            
            attendance_query = """
            INSERT INTO attendance 
            (member_id, date, time_in, time_out)
            VALUES (?, ?, ?, ?)
            """
            db_manager.execute_query(attendance_query, (
                member_id, attendance_date.date(), time_in, time_out
            ))
    
    print("تم إضافة الاشتراكات والمدفوعات التجريبية")

def main():
    """الدالة الرئيسية لإضافة البيانات التجريبية"""
    try:
        db_manager = DatabaseManager()
        db_manager.connect()
        
        print("بدء إضافة البيانات التجريبية...")
        
        # إضافة خطط الاشتراك
        add_sample_subscription_plans(db_manager)
        
        # إضافة الأعضاء
        member_ids = add_sample_members(db_manager)
        
        # إضافة الاشتراكات والمدفوعات
        add_sample_subscriptions_and_payments(db_manager, member_ids)
        
        print("تم إضافة جميع البيانات التجريبية بنجاح!")
        
    except Exception as e:
        print(f"خطأ في إضافة البيانات التجريبية: {e}")
    finally:
        if db_manager:
            db_manager.disconnect()

if __name__ == "__main__":
    main()
