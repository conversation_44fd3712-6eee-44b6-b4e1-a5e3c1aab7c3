# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول لنظام إدارة الصالة الرياضية
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QLineEdit, QPushButton, QMessageBox, QFrame)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QIcon

import config
from utils.helpers import verify_password
from ui.main_window import MainWindow

class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal()
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.main_window = None
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - " + config.APP_NAME)
        self.setFixedSize(400, 300)
        self.setWindowFlags(Qt.Window | Qt.WindowCloseButtonHint)
        
        # تطبيق الستايل
        self.setStyleSheet(f"""
            QWidget {{
                background-color: {config.COLORS['background']};
                font-family: {config.FONTS['main']};
            }}
        """)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(40, 40, 40, 40)
        
        # إطار تسجيل الدخول
        login_frame = QFrame()
        login_frame.setFrameStyle(QFrame.Box)
        login_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {config.COLORS['surface']};
                border: 2px solid {config.COLORS['primary']};
                border-radius: 10px;
                padding: 20px;
            }}
        """)
        
        login_layout = QVBoxLayout(login_frame)
        login_layout.setSpacing(15)
        
        # عنوان التطبيق
        title_label = QLabel(config.APP_NAME)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_title'], QFont.Bold))
        title_label.setStyleSheet(f"color: {config.COLORS['primary']};")
        
        # عنوان فرعي
        subtitle_label = QLabel("تسجيل دخول مسؤول النظام")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal']))
        subtitle_label.setStyleSheet(f"color: {config.COLORS['text_secondary']};")
        
        # حقل اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal']))
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        self.username_input.setStyleSheet(self.get_input_style())
        self.username_input.setText("admin")  # قيمة افتراضية للاختبار
        
        # حقل كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal']))
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(self.get_input_style())
        self.password_input.setText("admin123")  # قيمة افتراضية للاختبار
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal'], QFont.Bold))
        self.login_button.setStyleSheet(self.get_button_style())
        self.login_button.clicked.connect(self.login)
        
        # إضافة العناصر إلى التخطيط
        login_layout.addWidget(title_label)
        login_layout.addWidget(subtitle_label)
        login_layout.addWidget(QLabel())  # مساحة فارغة
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)
        login_layout.addWidget(QLabel())  # مساحة فارغة
        login_layout.addWidget(self.login_button)
        
        main_layout.addWidget(login_frame)
        self.setLayout(main_layout)
        
        # ربط Enter بتسجيل الدخول
        self.username_input.returnPressed.connect(self.login)
        self.password_input.returnPressed.connect(self.login)
        
        # تركيز على حقل اسم المستخدم
        self.username_input.setFocus()
        
        # توسيط النافذة
        self.center_window()
    
    def get_input_style(self):
        """ستايل حقول الإدخال"""
        return f"""
            QLineEdit {{
                padding: 10px;
                border: 2px solid {config.COLORS['surface']};
                border-radius: 5px;
                font-size: {config.FONTS['size_normal']}px;
                background-color: white;
            }}
            QLineEdit:focus {{
                border-color: {config.COLORS['primary']};
            }}
        """
    
    def get_button_style(self):
        """ستايل الأزرار"""
        return f"""
            QPushButton {{
                background-color: {config.COLORS['primary']};
                color: white;
                border: none;
                padding: 12px;
                border-radius: 5px;
                font-size: {config.FONTS['size_normal']}px;
            }}
            QPushButton:hover {{
                background-color: {config.COLORS['secondary']};
            }}
            QPushButton:pressed {{
                background-color: #1976D2;
            }}
        """
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_error("يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        try:
            # البحث عن المستخدم في قاعدة البيانات
            user = self.db_manager.fetch_one(
                "SELECT * FROM admin_users WHERE username = ?",
                (username,)
            )
            
            if user and verify_password(password, user['password_hash']):
                # تحديث وقت آخر تسجيل دخول
                self.db_manager.execute_query(
                    "UPDATE admin_users SET last_login = CURRENT_TIMESTAMP WHERE id = ?",
                    (user['id'],)
                )
                
                # إظهار النافذة الرئيسية
                self.show_main_window()
                
            else:
                self.show_error("اسم المستخدم أو كلمة المرور غير صحيحة")
                
        except Exception as e:
            self.show_error(f"خطأ في تسجيل الدخول: {str(e)}")
    
    def show_main_window(self):
        """إظهار النافذة الرئيسية"""
        try:
            self.main_window = MainWindow(self.db_manager)
            self.main_window.show()
            self.close()
        except Exception as e:
            self.show_error(f"خطأ في فتح النافذة الرئيسية: {str(e)}")
    
    def show_error(self, message):
        """إظهار رسالة خطأ"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("خطأ")
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
