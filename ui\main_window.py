# -*- coding: utf-8 -*-
"""
النافذة الرئيسية لنظام إدارة الصالة الرياضية
"""

from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                             QLabel, QPushButton, QFrame, QGridLayout, QMenuBar,
                             QAction, QMessageBox, QStackedWidget)
from PyQt5.QtCore import Qt, QTimer
from PyQt5.QtGui import QFont, QIcon

import config
from utils.helpers import format_currency

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self, db_manager):
        super().__init__()
        self.db_manager = db_manager
        self.setup_ui()
        self.setup_menu()
        self.load_dashboard_data()
        
        # تحديث البيانات كل 30 ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_dashboard_data)
        self.timer.start(30000)  # 30 ثانية
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle(config.APP_NAME)
        self.setMinimumSize(config.MIN_WINDOW_WIDTH, config.MIN_WINDOW_HEIGHT)
        self.resize(config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
        
        # تطبيق الستايل العام
        self.setStyleSheet(f"""
            QMainWindow {{
                background-color: {config.COLORS['background']};
                font-family: {config.FONTS['main']};
            }}
        """)
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # شريط العنوان
        title_bar = self.create_title_bar()
        main_layout.addWidget(title_bar)
        
        # لوحة المعلومات
        dashboard = self.create_dashboard()
        main_layout.addWidget(dashboard)
        
        # أزرار الوظائف الرئيسية
        functions_panel = self.create_functions_panel()
        main_layout.addWidget(functions_panel)
        
        # توسيط النافذة
        self.center_window()
    
    def create_title_bar(self):
        """إنشاء شريط العنوان"""
        title_frame = QFrame()
        title_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {config.COLORS['primary']};
                border-radius: 10px;
                padding: 15px;
            }}
        """)
        
        layout = QHBoxLayout(title_frame)
        
        # عنوان التطبيق
        title_label = QLabel(config.APP_NAME)
        title_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_title'], QFont.Bold))
        title_label.setStyleSheet("color: white;")
        
        # معلومات الإصدار
        version_label = QLabel(f"الإصدار {config.APP_VERSION}")
        version_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_small']))
        version_label.setStyleSheet("color: white;")
        version_label.setAlignment(Qt.AlignRight)
        
        layout.addWidget(title_label)
        layout.addStretch()
        layout.addWidget(version_label)
        
        return title_frame
    
    def create_dashboard(self):
        """إنشاء لوحة المعلومات"""
        dashboard_frame = QFrame()
        dashboard_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {config.COLORS['surface']};
                border: 1px solid {config.COLORS['primary']};
                border-radius: 10px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(dashboard_frame)
        
        # عنوان لوحة المعلومات
        dashboard_title = QLabel("لوحة المعلومات")
        dashboard_title.setFont(QFont(config.FONTS['main'], config.FONTS['size_large'], QFont.Bold))
        dashboard_title.setStyleSheet(f"color: {config.COLORS['primary']};")
        dashboard_title.setAlignment(Qt.AlignCenter)
        
        # شبكة الإحصائيات
        stats_grid = QGridLayout()
        stats_grid.setSpacing(15)
        
        # بطاقات الإحصائيات
        self.total_members_card = self.create_stat_card("إجمالي الأعضاء", "0", config.COLORS['primary'])
        self.active_members_card = self.create_stat_card("الأعضاء النشطين", "0", config.COLORS['success'])
        self.monthly_revenue_card = self.create_stat_card("الإيرادات الشهرية", "0 د.ل", config.COLORS['secondary'])
        self.expiring_subscriptions_card = self.create_stat_card("اشتراكات تنتهي قريباً", "0", config.COLORS['warning'])
        
        # ترتيب البطاقات في الشبكة
        stats_grid.addWidget(self.total_members_card, 0, 0)
        stats_grid.addWidget(self.active_members_card, 0, 1)
        stats_grid.addWidget(self.monthly_revenue_card, 1, 0)
        stats_grid.addWidget(self.expiring_subscriptions_card, 1, 1)
        
        layout.addWidget(dashboard_title)
        layout.addLayout(stats_grid)
        
        return dashboard_frame
    
    def create_stat_card(self, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 2px solid {color};
                border-radius: 8px;
                padding: 15px;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal']))
        title_label.setStyleSheet(f"color: {config.COLORS['text_secondary']};")
        title_label.setAlignment(Qt.AlignCenter)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont(config.FONTS['main'], config.FONTS['size_title'], QFont.Bold))
        value_label.setStyleSheet(f"color: {color};")
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addWidget(title_label)
        layout.addWidget(value_label)
        
        # حفظ مرجع للقيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
    
    def create_functions_panel(self):
        """إنشاء لوحة الوظائف الرئيسية"""
        functions_frame = QFrame()
        functions_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {config.COLORS['surface']};
                border: 1px solid {config.COLORS['primary']};
                border-radius: 10px;
                padding: 20px;
            }}
        """)
        
        layout = QVBoxLayout(functions_frame)
        
        # عنوان الوظائف
        functions_title = QLabel("الوظائف الرئيسية")
        functions_title.setFont(QFont(config.FONTS['main'], config.FONTS['size_large'], QFont.Bold))
        functions_title.setStyleSheet(f"color: {config.COLORS['primary']};")
        functions_title.setAlignment(Qt.AlignCenter)
        
        # شبكة الأزرار
        buttons_grid = QGridLayout()
        buttons_grid.setSpacing(15)
        
        # أزرار الوظائف
        members_btn = self.create_function_button("إدارة الأعضاء", self.open_members_management)
        plans_btn = self.create_function_button("خطط الاشتراك", self.open_subscription_plans)
        payments_btn = self.create_function_button("إدارة المدفوعات", self.open_payments_management)
        reports_btn = self.create_function_button("التقارير", self.open_reports)
        backup_btn = self.create_function_button("النسخ الاحتياطي", self.open_backup_management)
        settings_btn = self.create_function_button("الإعدادات", self.open_settings)
        
        # ترتيب الأزرار في الشبكة
        buttons_grid.addWidget(members_btn, 0, 0)
        buttons_grid.addWidget(plans_btn, 0, 1)
        buttons_grid.addWidget(payments_btn, 0, 2)
        buttons_grid.addWidget(reports_btn, 1, 0)
        buttons_grid.addWidget(backup_btn, 1, 1)
        buttons_grid.addWidget(settings_btn, 1, 2)
        
        layout.addWidget(functions_title)
        layout.addLayout(buttons_grid)
        
        return functions_frame
    
    def create_function_button(self, text, callback):
        """إنشاء زر وظيفة"""
        button = QPushButton(text)
        button.setFont(QFont(config.FONTS['main'], config.FONTS['size_normal'], QFont.Bold))
        button.setMinimumHeight(60)
        button.setStyleSheet(f"""
            QPushButton {{
                background-color: {config.COLORS['primary']};
                color: white;
                border: none;
                border-radius: 8px;
                padding: 15px;
            }}
            QPushButton:hover {{
                background-color: {config.COLORS['secondary']};
            }}
            QPushButton:pressed {{
                background-color: #1976D2;
            }}
        """)
        button.clicked.connect(callback)
        return button

    def setup_menu(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة الملف
        file_menu = menubar.addMenu('ملف')

        backup_action = QAction('نسخ احتياطي', self)
        backup_action.triggered.connect(self.open_backup_management)
        file_menu.addAction(backup_action)

        file_menu.addSeparator()

        exit_action = QAction('خروج', self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # قائمة الإدارة
        management_menu = menubar.addMenu('إدارة')

        members_action = QAction('الأعضاء', self)
        members_action.triggered.connect(self.open_members_management)
        management_menu.addAction(members_action)

        plans_action = QAction('خطط الاشتراك', self)
        plans_action.triggered.connect(self.open_subscription_plans)
        management_menu.addAction(plans_action)

        payments_action = QAction('المدفوعات', self)
        payments_action.triggered.connect(self.open_payments_management)
        management_menu.addAction(payments_action)

        # قائمة التقارير
        reports_menu = menubar.addMenu('تقارير')

        financial_reports_action = QAction('التقارير المالية', self)
        financial_reports_action.triggered.connect(self.open_reports)
        reports_menu.addAction(financial_reports_action)

        # قائمة المساعدة
        help_menu = menubar.addMenu('مساعدة')

        about_action = QAction('حول البرنامج', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def load_dashboard_data(self):
        """تحميل بيانات لوحة المعلومات"""
        try:
            # إجمالي الأعضاء
            total_members = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM members"
            )['count']
            self.total_members_card.value_label.setText(str(total_members))

            # الأعضاء النشطين
            active_members = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM members WHERE status = 'نشط'"
            )['count']
            self.active_members_card.value_label.setText(str(active_members))

            # الإيرادات الشهرية
            from datetime import datetime
            current_month = datetime.now().strftime('%Y-%m')
            monthly_revenue = self.db_manager.fetch_one(
                "SELECT COALESCE(SUM(amount_lyd), 0) as total FROM payments WHERE strftime('%Y-%m', payment_date) = ? AND status = 'مكتملة'",
                (current_month,)
            )['total']
            self.monthly_revenue_card.value_label.setText(format_currency(monthly_revenue))

            # الاشتراكات التي تنتهي خلال 7 أيام
            expiring_subscriptions = self.db_manager.fetch_one(
                "SELECT COUNT(*) as count FROM subscriptions WHERE end_date BETWEEN date('now') AND date('now', '+7 days') AND status = 'نشط'"
            )['count']
            self.expiring_subscriptions_card.value_label.setText(str(expiring_subscriptions))

        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة المعلومات: {e}")

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    # وظائف فتح النوافذ المختلفة
    def open_members_management(self):
        """فتح إدارة الأعضاء"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير إدارة الأعضاء قريباً")

    def open_subscription_plans(self):
        """فتح إدارة خطط الاشتراك"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير إدارة خطط الاشتراك قريباً")

    def open_payments_management(self):
        """فتح إدارة المدفوعات"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير إدارة المدفوعات قريباً")

    def open_reports(self):
        """فتح التقارير"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير التقارير قريباً")

    def open_backup_management(self):
        """فتح إدارة النسخ الاحتياطي"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير إدارة النسخ الاحتياطي قريباً")

    def open_settings(self):
        """فتح الإعدادات"""
        QMessageBox.information(self, "قريباً", "سيتم تطوير الإعدادات قريباً")

    def show_about(self):
        """إظهار معلومات حول البرنامج"""
        about_text = f"""
        {config.APP_NAME}
        الإصدار {config.APP_VERSION}

        نظام إدارة شامل للصالة الرياضية

        المطور: {config.APP_AUTHOR}
        """
        QMessageBox.about(self, "حول البرنامج", about_text)

    def closeEvent(self, event):
        """التعامل مع إغلاق النافذة"""
        reply = QMessageBox.question(
            self, 'تأكيد الخروج',
            'هل أنت متأكد من الخروج من البرنامج؟',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إيقاف المؤقت
            if hasattr(self, 'timer'):
                self.timer.stop()
            # إغلاق اتصال قاعدة البيانات
            if self.db_manager:
                self.db_manager.disconnect()
            event.accept()
        else:
            event.ignore()
